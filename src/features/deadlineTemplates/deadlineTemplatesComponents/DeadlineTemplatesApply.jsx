import { Formik, Form } from "formik";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  <PERSON>rid,
  Header,
  Segment,
  Table,
} from "semantic-ui-react";
import { toast } from "react-toastify";
import { useMediaQuery } from "react-responsive";
import { Calendar as RBCalendar, dateFnsLocalizer } from "react-big-calendar";
import withDragAndDrop from "react-big-calendar/lib/addons/dragAndDrop";
import "react-big-calendar/lib/css/react-big-calendar.css";
import "react-big-calendar/lib/addons/dragAndDrop/styles.css";
import { closeModal } from "../../../app/common/modals/modalSlice";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import MyDateInput from "../../../app/common/form/MyDateInput";
import MyNumberInput from "../../../app/common/form/MyNumberInput";
import MyRadioButtons from "../../../app/common/form/MyRadioButtons";
import {
  daysOfWeekArray,
  startingDateOptions,
  tableForDeadlineTemplates,
} from "../../../app/common/categoryData/categoryOptions";
import {
  addDays,
  differenceInCalendarDays,
  format,
  getDay,
  parse,
  startOfWeek,
  subDays,
} from "date-fns";
import { updateDocInDb } from "../../../app/firestore/firestoreService";
import { deadlineTemplatesFormFieldMapping } from "./DeadlineTemplatesFormFieldMapping";

const DnDCalendar = withDragAndDrop(RBCalendar);

const locales = {
  "en-US": require("date-fns/locale/en-US"),
};

const calendarLocalizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

function getDeadlineDate(values, deadlineName) {
  const deadlineConfig = values[deadlineName];
	  if (
	    !deadlineConfig ||
	    deadlineConfig.days === undefined ||
	    deadlineConfig.days === null ||
	    !deadlineConfig.startingDateType
	  ) {
	    return null;
	  }

	  const numberOfDays = Number(deadlineConfig.days);
	  const startingDateType = deadlineConfig.startingDateType;
	  const closingDateDays =
	    values["closingDate"] && values["closingDate"]["days"];

	  let startingDate = null;
	  let deadlineDate = null;

	  if (startingDateType === "Contract") {
	    if (!values.contractDateTime) return null;
	    deadlineDate = addDays(values.contractDateTime, numberOfDays);
	  } else if (startingDateType === "Closing") {
	    if (
	      !values.contractDateTime ||
	      closingDateDays === undefined ||
	      closingDateDays === null
	    ) {
	      return null;
	    }
	    startingDate = addDays(values.contractDateTime, closingDateDays);
	    deadlineDate = subDays(startingDate, numberOfDays);
	  } else if (startingDateType === "MEC") {
	    if (!values.mecDateTime) return null;
	    deadlineDate = addDays(values.mecDateTime, numberOfDays);
	  }

	  if (!deadlineDate) return null;
	  return new Date(deadlineDate);
}

function moveDeadlineOnCalendar(deadlineName, targetDate, values, setFieldValue) {
  if (!deadlineName || !targetDate) return;

  const deadlineConfig = values[deadlineName];
  if (!deadlineConfig || !deadlineConfig.startingDateType) return;

  const startingDateType = deadlineConfig.startingDateType;
  const closingDateConfig = values["closingDate"];
  const closingDateDays = closingDateConfig && closingDateConfig["days"];

  let newDays = null;

  if (startingDateType === "Contract") {
    if (!values.contractDateTime) {
      toast.error(
        "Please set a Contract Date before adjusting this deadline on the calendar."
      );
      return;
    }
    newDays = differenceInCalendarDays(targetDate, values.contractDateTime);
  } else if (startingDateType === "MEC") {
    if (!values.mecDateTime) {
      toast.error(
        "Please set an MEC Date before adjusting this deadline on the calendar."
      );
      return;
    }
    newDays = differenceInCalendarDays(targetDate, values.mecDateTime);
  } else if (startingDateType === "Closing") {
    if (!values.contractDateTime || closingDateDays === undefined) {
      toast.error(
        "Please set both Contract Date and Closing Date days before adjusting this deadline on the calendar."
      );
      return;
    }
    const closingDate = addDays(values.contractDateTime, closingDateDays);
    newDays = differenceInCalendarDays(closingDate, targetDate);
  }

  if (newDays === null || Number.isNaN(newDays)) return;

  setFieldValue(`${deadlineName}.days`, newDays);
}

export default function DeadlineTemplatesApply({ deadlineTemplate }) {
  const dispatch = useDispatch();
  const { transaction } = useSelector((state) => state.transaction);
  const { doc } = useSelector((state) => state.doc);
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const deadlines = tableForDeadlineTemplates;
  const [viewMode, setViewMode] = React.useState("table");
  const [selectedDeadlineName, setSelectedDeadlineName] = React.useState(null);
	const initialValues = React.useMemo(
    () => ({
      ...deadlineTemplate,
      contractDateTime: transaction.contractDateTime || new Date(),
      mecDateTime: transaction.mecDateTime || null,
      closingDateTime: transaction.closingDateTime || null,
    }),
    [
      deadlineTemplate,
      transaction.contractDateTime,
      transaction.mecDateTime,
      transaction.closingDateTime,
    ]
  );

  function applyDeadlineTemplateToForm(values) {
    let updateFields = {};
    const closingDateDays =
      values["closingDate"] && values["closingDate"]["days"];
    Object.values(values).forEach((value) => {
      if (
        value &&
        value.startingDateType &&
        value.event &&
        value.days !== undefined &&
        value.days !== null
      ) {
        const numberOfDays = Number(value.days);
        let startingDate = null;
        let deadlineDate = null;
        if (value.startingDateType === "Contract") {
          if (!values.contractDateTime) return;
          deadlineDate = addDays(values.contractDateTime, numberOfDays);
        } else if (value.startingDateType === "Closing") {
          if (
            !values.contractDateTime ||
            closingDateDays === undefined ||
            closingDateDays === null
          ) {
            return;
          }
          startingDate = addDays(values.contractDateTime, closingDateDays);
          deadlineDate = subDays(startingDate, numberOfDays);
        } else if (value.startingDateType === "MEC") {
          if (!values.mecDateTime) return;
          deadlineDate = addDays(values.mecDateTime, numberOfDays);
        }
        if (deadlineDate) {
          const formFieldName = deadlineTemplatesFormFieldMapping[value.event];
          if (formFieldName !== "DateAlternative Earnest Money Deadline")
            // FIX ERROR LATER 2025 can't remember error but i think some forms dont have this field maybe? can we check if this formfield exists?
            updateFields[`formFieldValues.${formFieldName}`] = new Date(
              deadlineDate
            );
        }
      }
    });
    updateDocInDb(doc.id, updateFields);
  }

  function calculateDayOfWeek(type, values, deadlineName) {
    // console.log(values[deadlineName]);
    const numberOfDays = values[deadlineName]["days"];
    if (numberOfDays === undefined || numberOfDays === null) {
      return;
    }
    const daysNumber = Number(numberOfDays);
    const startingDateType = values[deadlineName]["startingDateType"];
    const closingDateDays =
      values["closingDate"] && values["closingDate"]["days"];
    let dayOfWeekAsNumber = null;
    let startingDate = null;
    let deadlineDate = null;
    if (startingDateType === "Contract") {
      deadlineDate =
        values.contractDateTime && addDays(values.contractDateTime, daysNumber);
    } else if (startingDateType === "Closing") {
      if (
        !values.contractDateTime ||
        closingDateDays === undefined ||
        closingDateDays === null
      ) {
        return;
      }
      startingDate = addDays(values.contractDateTime, closingDateDays);
      deadlineDate = subDays(startingDate, daysNumber);
    } else if (startingDateType === "MEC") {
      deadlineDate =
        values.mecDateTime && addDays(values.mecDateTime, daysNumber);
    }
    if (!deadlineDate) {
      return;
    }
    if (type === "date") {
      return format(new Date(deadlineDate), "MM/dd/yyyy");
    } else {
      dayOfWeekAsNumber = getDay(deadlineDate);
      return daysOfWeekArray[dayOfWeekAsNumber];
    }
  }

  return (
    <ModalWrapper size="fullscreen">
      <Segment clearing>
        <div className="medium horizontal margin small top margin">
          <Formik
            enableReinitialize
            initialValues={initialValues}
            validateOnChange={false}
            validateOnBlur={false}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                applyDeadlineTemplateToForm(values);
                setSubmitting(false);
                dispatch(
                  closeModal({
                    modalType: "DeadlineTemplatesApply",
                  })
                );
                toast.success("Deadline template successfully applied");
                setTimeout(function () {
                  window.location.reload();
                }, 100);
              } catch (error) {
                toast.error(error.message);
                setSubmitting(false);
              }
            }}
          >
            {({ isSubmitting, values, setFieldValue }) => {
              const calendarEvents =
                deadlines &&
                deadlines
                  .filter((deadline) => !deadline.isTopic)
                  .map((deadline) => {
                    const date = getDeadlineDate(values, deadline.name);
                    if (!date) return null;
                    return {
                      id: deadline.name,
                      title: deadline.event,
                      start: date,
                      end: date,
                      allDay: true,
                    };
                  })
                  .filter((event) => event);

              return (
                <Form className="ui form" autoComplete="off">
                  <Header size="large" color="blue">
                    Apply Deadlines to Form
                  </Header>
                  <p color="grey">
                    Dates will be automatically calculated based on either: + X
                    days after contract date, - X days before closing date, or +
                    X days after MEC.
                    <br />
                    Adjust dates here if desired by modifying the # of days, for
                    example if it lands on a weekend.
                  </p>
                  <Divider />
                  <Grid>
                    <Grid.Row className="small vertical padding">
                      <Grid.Column
                        mobile={16}
                        computer={16}
                        className="small top margin"
                      >
                        <Header as="h3" color="blue">
                          Template Name: {deadlineTemplate.name}
                        </Header>
                      </Grid.Column>
                    </Grid.Row>
                    <Grid.Row className="tiny vertical padding">
                      <Grid.Column mobile={16} computer={2}></Grid.Column>

                      <Grid.Column mobile={16} computer={3}>
                        <MyDateInput
                          name="contractDateTime"
                          label="Contract Date"
                        />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={3}>
                        <MyDateInput name="mecDateTime" label="MEC Date" />
                      </Grid.Column>
                      <Grid.Column mobile={16} computer={3}>
                        <MyDateInput
                          name="closingDateTime"
                          label="Closing Date"
                        />
                      </Grid.Column>
                      <Grid.Column
                        mobile={16}
                        computer={4}
                        className="small top margin"
                      >
                        <Button.Group size="small">
                          <Button
                            active={viewMode === "table"}
                            type="button"
                            onClick={() => setViewMode("table")}
                          >
                            Table
                          </Button>
                          <Button
                            active={viewMode === "calendar"}
                            type="button"
                            onClick={() => setViewMode("calendar")}
                          >
                            Calendar
                          </Button>
                        </Button.Group>
                      </Grid.Column>
                    </Grid.Row>
                  </Grid>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginTop: "8px",
                      marginBottom: "8px",
                    }}
                  >
                    <Header as="h4" color="blue" style={{ margin: 0 }}>
                      Deadlines
                    </Header>
                  </div>

                  {viewMode === "table" && (
                    <Table compact sortable celled>
                      <Table.Header className="white-blue-table-header">
                        <Table.Row>
                          <Table.HeaderCell></Table.HeaderCell>
                          <Table.HeaderCell></Table.HeaderCell>
                          <Table.HeaderCell>Event</Table.HeaderCell>
                          <Table.HeaderCell>
                            Contract | Closing | MEC
                          </Table.HeaderCell>
                          <Table.HeaderCell>Days</Table.HeaderCell>
                          <Table.HeaderCell>Date</Table.HeaderCell>
                          <Table.HeaderCell>Day of Week</Table.HeaderCell>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {deadlines.length !== 0 &&
                          deadlines.map((deadline) => (
                            <Table.Row
                              key={deadline.event}
                              className={
                                deadline.isTopic ? "table-row-blue" : null
                              }
                            >
                              {deadline.isTopic ? (
                                <>
                                  <Table.Cell></Table.Cell>
                                  <Table.Cell></Table.Cell>
                                  <Table.Cell>{deadline.event}</Table.Cell>
                                  <Table.Cell></Table.Cell>
                                  <Table.Cell></Table.Cell>
                                  <Table.Cell></Table.Cell>
                                  <Table.Cell></Table.Cell>
                                </>
                              ) : (
                                <>
                                  <Table.Cell collapsing>
                                    {deadline.itemNumber}
                                  </Table.Cell>
                                  <Table.Cell collapsing>
                                    {deadline.reference}
                                  </Table.Cell>
                                  <Table.Cell>{deadline.event}</Table.Cell>
                                  <Table.Cell>
                                    <MyRadioButtons
                                      name={deadline.name + ".startingDateType"}
                                      options={startingDateOptions}
                                      noLabels={true}
                                      size={"small"}
                                    />
                                  </Table.Cell>
                                  <Table.Cell style={{ maxWidth: "80px" }}>
                                    <MyNumberInput
                                      name={deadline.name + ".days"}
                                    />
                                  </Table.Cell>
                                  <Table.Cell>
                                    {calculateDayOfWeek(
                                      "day",
                                      values,
                                      deadline.name
                                    )}
                                  </Table.Cell>
                                  <Table.Cell>
                                    {calculateDayOfWeek(
                                      "date",
                                      values,
                                      deadline.name
                                    )}
                                  </Table.Cell>
                                </>
                              )}
                            </Table.Row>
                          ))}
                      </Table.Body>
                    </Table>
                  )}

                  {viewMode === "calendar" && (
                    <div>
                      <p className="small bottom margin">
                        Click a deadline event below to select it, then click a
                        new date on the calendar to move that deadline.
                      </p>
                      <DnDCalendar
                        localizer={calendarLocalizer}
                        events={calendarEvents}
                        startAccessor="start"
                        endAccessor="end"
                        views={["month"]}
                        popup
                        selectable
                        defaultDate={values.contractDateTime || new Date()}
                        style={{
                          height: 750,
                          marginTop: "8px",
                          width: "100%",
                        }}
                        onEventDrop={({ event, start }) => {
                          if (!event || !start) return;
                          moveDeadlineOnCalendar(
                            event.id,
                            start,
                            values,
                            setFieldValue
                          );
                        }}
                        onSelectEvent={(event) => {
                          setSelectedDeadlineName(event.id);
                        }}
                        onSelectSlot={(slotInfo) => {
                          if (!selectedDeadlineName) return;
                          moveDeadlineOnCalendar(
                            selectedDeadlineName,
                            slotInfo.start,
                            values,
                            setFieldValue
                          );
                          setSelectedDeadlineName(null);
                        }}
                        eventPropGetter={(event) => {
                          const isSelected = event.id === selectedDeadlineName;
                          const backgroundColor = isSelected
                            ? "#f2711c"
                            : "#57b2ff";
                          return {
                            style: {
                              backgroundColor,
                              color: "white",
                            },
                          };
                        }}
                      />
                    </div>
                  )}
                  <Divider className="medium top margin" />
                  <Button
                    loading={isSubmitting}
                    type="submit"
                    floated={isMobile ? null : "right"}
                    primary
                    content="Apply"
                    className={isMobile ? "fluid medium bottom margin" : null}
                  />
                  <Button
                    disabled={isSubmitting}
                    onClick={() =>
                      dispatch(
                        closeModal({
                          modalType: "DeadlineTemplatesApply",
                        })
                      )
                    }
                    to="#"
                    type="button"
                    floated={isMobile ? null : "right"}
                    content="Cancel"
                    className={isMobile ? "fluid medium bottom margin" : null}
                  />
                </Form>
              );
            }}
          </Formik>
        </div>
      </Segment>
    </ModalWrapper>
  );
}
